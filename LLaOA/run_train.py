#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Run training script for LLaOA - Projector-Only Training Mode.
This script supports only training the projector while freezing the omics encoder and language model.
"""

import os
import sys
import argparse
from typing import Optional
from pathlib import Path

# Ensure the package is in the Python path
current_dir = Path(__file__).parent.absolute()
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

def parse_args():
    parser = argparse.ArgumentParser(description="Run LLaOA Projector-Only Training")

    # Model arguments
    parser.add_argument("--model-path", type=str, dest="model_path",
                        help="Path to existing LLaOA model (for continued training)")
    parser.add_argument("--model-base", type=str, dest="model_base",
                        help="Path to base language model (e.g., llama-2-7b-chat-hf)")
    parser.add_argument("--compass-model-path", type=str, required=True,
                        help="Path to COMPASS model")
    parser.add_argument("--feature-type", type=str, default="gene_level",
                        choices=["gene_level", "geneset_level", "concept_level", "vector"],
                        help="Type of features to extract from COMPASS")
    parser.add_argument("--projector-type", type=str, default="mlp2x_gelu",
                        choices=["linear", "mlp2x_gelu", "mlp3x_gelu"],
                        help="Type of projector to use")
    parser.add_argument("--tune-projector-only", action="store_true", default=True,
                        help="Train only the projector (freeze encoder and LM)")

    # Data arguments
    parser.add_argument("--rna-seq-path", type=str, required=True,
                        help="Path to RNAseq data")
    parser.add_argument("--qa-json-path", type=str, required=True,
                        help="Path to QA pairs JSON")
    parser.add_argument("--sample-id-col", type=str, default="sample_id",
                        help="Column name for sample ID in RNAseq data")
    parser.add_argument("--max-length", type=int, default=None,
                        help="Maximum sequence length (if None, uses tokenizer.model_max_length)")
    parser.add_argument("--validation-split", type=float, default=0.1,
                        help="Fraction of data to use for validation")
    parser.add_argument("--seed", type=int, default=42,
                        help="Random seed")

    # Training arguments
    parser.add_argument("--run-name", type=str, default=None,
                        help="Name for this training run (used for both run identification and TensorBoard run naming)")
    parser.add_argument("--output-dir", type=str, default="./output",
                        help="Output directory to save model checkpoints, TensorBoard logs, etc.")
    parser.add_argument("--report-to", type=str, default="tensorboard", choices=["none", "tensorboard"],
                        help="Experiment tracking service (use 'tensorboard' to enable, 'none' to disable)")
    parser.add_argument("--num-train-epochs", type=float, default=3.0,
                        help="Number of training epochs")
    parser.add_argument("--per-device-train-batch-size", type=int, default=4,
                        help="Batch size per device for training")
    parser.add_argument("--per-device-eval-batch-size", type=int, default=4,
                        help="Batch size per device for evaluation")
    parser.add_argument("--gradient-accumulation-steps", type=int, default=1,
                        help="Number of gradient accumulation steps")
    parser.add_argument("--learning-rate", type=float, default=1e-5,
                        help="Learning rate")
    parser.add_argument("--weight-decay", type=float, default=0.0,
                        help="Weight decay")
    parser.add_argument("--warmup-steps", type=int, default=100,
                        help="Number of warmup steps")
    parser.add_argument("--warmup-ratio", type=float, default=None,
                        help="Warmup ratio (alternative to warmup-steps)")
    parser.add_argument("--lr-scheduler-type", type=str, default="linear",
                        choices=["linear", "cosine", "cosine_with_restarts", "polynomial", "constant"],
                        help="Type of learning rate scheduler")
    parser.add_argument("--logging-steps", type=int, default=10,
                        help="Number of steps between logging")
    parser.add_argument("--eval-steps", type=int, default=100,
                        help="Number of steps between evaluations")
    parser.add_argument("--save-steps", type=int, default=500,
                        help="Number of steps between saving checkpoints")
    parser.add_argument("--evaluation-strategy", type=str, default="steps",
                        choices=["no", "steps", "epoch"],
                        help="Evaluation strategy")
    parser.add_argument("--save-strategy", type=str, default="steps",
                        choices=["no", "steps", "epoch"],
                        help="Save strategy")
    parser.add_argument("--save-total-limit", type=int, default=3,
                        help="Maximum number of checkpoints to keep")
    parser.add_argument("--load-best-model-at-end", action="store_true",
                        help="Load best model at end of training")
    parser.add_argument("--metric-for-best-model", type=str, default="eval_loss",
                        help="Metric to use for best model selection")
    parser.add_argument("--greater-is-better", action="store_true",
                        help="Whether higher metric values are better")
    parser.add_argument("--resume-from-checkpoint", type=str, default=None,
                        help="Path to checkpoint directory to resume training from")
    parser.add_argument("--fp16", action="store_true",
                        help="Use mixed precision training (fp16)")
    parser.add_argument("--bf16", action="store_true",
                        help="Use mixed precision training (bf16)")
    parser.add_argument("--gradient-checkpointing", action="store_true",
                        help="Use gradient checkpointing to save memory")
    parser.add_argument("--dataloader-num-workers", type=int, default=4,
                        help="Number of dataloader workers")
    parser.add_argument("--local_rank", type=int, default=-1,
                        help="Local rank for distributed training")

    return parser.parse_args()

def main():
    args = parse_args()

    # Validate arguments
    if not args.model_path and not args.model_base:
        raise ValueError("Either --model-path or --model-base must be provided")

    # Set up directory structure: <output-dir>/<run-name>/
    if args.run_name:
        base_output_dir = os.path.join(args.output_dir, args.run_name)
        # Create checkpoints and logs subdirectories
        checkpoints_dir = os.path.join(base_output_dir, "checkpoints")
        logs_dir = os.path.join(base_output_dir, "logs")
    else:
        # If no run name specified, use original output dir
        base_output_dir = args.output_dir
        checkpoints_dir = args.output_dir
        logs_dir = os.path.join(args.output_dir, "logs")

    # Convert arguments to format expected by HfArgumentParser
    model_args = []

    # Add model path arguments
    if args.model_path:
        model_args.append(f"--model_path={args.model_path}")
    if args.model_base:
        model_args.append(f"--model_base={args.model_base}")

    # Add other model arguments
    model_args.extend([
        f"--compass_model_path={args.compass_model_path}",
        f"--feature_type={args.feature_type}",
        f"--projector_type={args.projector_type}",
    ])

    if args.tune_projector_only:
        model_args.append("--tune_projector_only")

    data_args = [
        f"--rna_seq_path={args.rna_seq_path}",
        f"--qa_json_path={args.qa_json_path}",
        f"--sample_id_col={args.sample_id_col}" if args.sample_id_col else "",
        f"--validation_split={args.validation_split}",
    ]

    # Add max_length if specified
    if args.max_length is not None:
        data_args.append(f"--max_length={args.max_length}")

    # Training arguments - use checkpoints directory for model saving
    training_args = [
        f"--output_dir={checkpoints_dir}",
        f"--base_output_dir={base_output_dir}",  # Pass base directory for TensorBoard
        f"--num_train_epochs={args.num_train_epochs}",
        f"--per_device_train_batch_size={args.per_device_train_batch_size}",
        f"--per_device_eval_batch_size={args.per_device_eval_batch_size}",
        f"--gradient_accumulation_steps={args.gradient_accumulation_steps}",
        f"--learning_rate={args.learning_rate}",
        f"--weight_decay={args.weight_decay}",
        f"--warmup_steps={args.warmup_steps}",
        f"--lr_scheduler_type={args.lr_scheduler_type}",
        f"--logging_steps={args.logging_steps}",
        f"--eval_steps={args.eval_steps}",
        f"--save_steps={args.save_steps}",
        f"--save_total_limit={args.save_total_limit}",
        f"--seed={args.seed}",
        f"--dataloader_num_workers={args.dataloader_num_workers}",
        f"--local_rank={args.local_rank}",
        f"--report_to={args.report_to}",
    ]



    # Add optional arguments that are supported
    if args.resume_from_checkpoint:
        training_args.append(f"--resume_from_checkpoint={args.resume_from_checkpoint}")
    if args.fp16:
        training_args.append("--fp16")
    if args.bf16:
        training_args.append("--bf16")

    # Filter out empty arguments
    model_args = [arg for arg in model_args if arg]
    data_args = [arg for arg in data_args if arg]
    training_args = [arg for arg in training_args if arg]

    # Combine all arguments
    all_args = model_args + data_args + training_args

    # Update sys.argv
    sys.argv = [sys.argv[0]] + all_args

    # Import and run training
    from llaoa.train.train import train
    train()

if __name__ == "__main__":
    main()
