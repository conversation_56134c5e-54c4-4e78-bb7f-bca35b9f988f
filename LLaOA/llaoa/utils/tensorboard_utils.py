#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TensorBoard utilities for LLaOA training.
Handles TensorBoard logging setup, directory management, and metric logging.
"""

import os
import sys
import json
import psutil
import platform
import io
import numpy as np
from pathlib import Path
from typing import Optional, Dict, Any, Union
from datetime import datetime

try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_AVAILABLE = True
except ImportError:
    TENSORBOARD_AVAILABLE = False
    SummaryWriter = None

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    torch = None

try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    plt = None


class TensorBoardConfig:
    """Simplified configuration class for TensorBoard integration."""

    def __init__(
        self,
        output_dir: str = "./checkpoints",
        run_name: Optional[str] = None
    ):
        """
        Initialize TensorBoard configuration.

        Args:
            output_dir: Training output directory
            run_name: Run name for TensorBoard. If None, derived from output_dir
        """
        self.output_dir = Path(output_dir)

        # Set run name based on output directory if not provided
        if run_name is None:
            self.run_name = self._extract_run_name_from_output_dir()
            # Create TensorBoard logs directory: <output_dir>/<run_name>/logs/tensorboard/
            self.tensorboard_dir = self.output_dir / self.run_name / "logs" / "tensorboard"
        else:
            self.run_name = run_name
            # If run_name is provided, assume output_dir is already the base directory
            # Create TensorBoard logs directory: <output_dir>/logs/tensorboard/
            self.tensorboard_dir = self.output_dir / "logs" / "tensorboard"

        # Ensure logs directory exists
        self.tensorboard_dir.mkdir(parents=True, exist_ok=True)

    def _extract_run_name_from_output_dir(self) -> str:
        """Extract run name from output directory path."""
        folder_name = self.output_dir.name

        # Use the full folder name as run name
        return folder_name



def initialize_tensorboard(
    tensorboard_config: TensorBoardConfig,
    model_args: Any = None,
    data_args: Any = None,
    training_args: Any = None,
    accelerator: Any = None
):
    """
    Initialize TensorBoard for training logging.

    Args:
        tensorboard_config: TensorBoardConfig instance
        model_args: Model arguments to log (optional)
        data_args: Data arguments to log (optional)
        training_args: Training arguments to log (optional)
        accelerator: Accelerator instance for distributed training

    Returns:
        SummaryWriter object if successful, None otherwise
    """
    if not TENSORBOARD_AVAILABLE:
        print("Warning: TensorBoard not available, skipping TensorBoard initialization")
        return None

    # Only initialize TensorBoard on main process
    if accelerator is not None and not accelerator.is_main_process:
        return None

    try:
        # Initialize TensorBoard SummaryWriter
        writer = SummaryWriter(
            log_dir=str(tensorboard_config.tensorboard_dir)
        )

        print(f"✓ Initialized TensorBoard logging")
        print(f"✓ Run name: {tensorboard_config.run_name}")
        print(f"✓ Logs directory: {tensorboard_config.tensorboard_dir}")
        print(f"✓ View with: tensorboard --logdir {tensorboard_config.tensorboard_dir.parent}")

        return writer

    except Exception as e:
        print(f"Warning: Failed to initialize TensorBoard: {e}")
        return None


def log_metrics(writer: Optional[SummaryWriter], metrics: Dict[str, Union[float, int]], step: Optional[int] = None):
    """
    Log metrics to TensorBoard.
    
    Args:
        writer: TensorBoard SummaryWriter instance
        metrics: Dictionary of metrics to log
        step: Optional step number
    """
    if writer is None:
        return
        
    try:
        for key, value in metrics.items():
            if isinstance(value, (int, float)):
                writer.add_scalar(key, value, step)
    except Exception as e:
        print(f"Warning: Failed to log metrics to TensorBoard: {e}")


def log_model_info(writer: Optional[SummaryWriter], model: Any, tokenizer: Any = None):
    """
    Log model information to TensorBoard.
    
    Args:
        writer: TensorBoard SummaryWriter instance
        model: Model to log information about
        tokenizer: Optional tokenizer to log information about
    """
    if writer is None:
        return
        
    try:
        # Log model parameters
        if hasattr(model, 'parameters'):
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            
            writer.add_scalar("model/total_parameters", total_params, 0)
            writer.add_scalar("model/trainable_parameters", trainable_params, 0)
            writer.add_scalar("model/trainable_percentage", 
                            100 * trainable_params / total_params if total_params > 0 else 0, 0)
            
        # Log tokenizer info
        if tokenizer is not None:
            if hasattr(tokenizer, '__len__'):
                writer.add_scalar("tokenizer/vocab_size", len(tokenizer), 0)
            if hasattr(tokenizer, 'model_max_length'):
                writer.add_scalar("tokenizer/model_max_length", tokenizer.model_max_length, 0)
            
    except Exception as e:
        print(f"Warning: Failed to log model info to TensorBoard: {e}")


def log_embedding_visualization(
    writer: Optional[SummaryWriter],
    scatter_figure: Any,
    stats_figure: Any,
    step: Optional[int] = None,
    tag_prefix: str = "embeddings"
):
    """
    Log embedding visualization figures to TensorBoard.

    Args:
        writer: TensorBoard SummaryWriter instance
        scatter_figure: matplotlib Figure with scatter plot
        stats_figure: matplotlib Figure with statistics plots
        step: Optional step number
        tag_prefix: Prefix for the TensorBoard tags
    """
    if writer is None or not MATPLOTLIB_AVAILABLE:
        return

    try:
        # Convert scatter plot to image and log
        if scatter_figure is not None:
            scatter_img = figure_to_image(scatter_figure)
            writer.add_image(f"{tag_prefix}/umap_scatter", scatter_img, step)
            plt.close(scatter_figure)  # Close to free memory

        # Convert statistics plot to image and log
        if stats_figure is not None:
            stats_img = figure_to_image(stats_figure)
            writer.add_image(f"{tag_prefix}/statistics", stats_img, step)
            plt.close(stats_figure)  # Close to free memory

    except Exception as e:
        print(f"Warning: Failed to log embedding visualizations to TensorBoard: {e}")


def figure_to_image(figure: Any) -> np.ndarray:
    """
    Convert a matplotlib figure to a numpy array for TensorBoard logging.

    Args:
        figure: matplotlib Figure object

    Returns:
        numpy array with shape [C, H, W] for TensorBoard
    """
    if not MATPLOTLIB_AVAILABLE:
        raise ImportError("Matplotlib is not available")

    # Save figure to a BytesIO buffer
    buf = io.BytesIO()
    figure.savefig(buf, format='png', dpi=100, bbox_inches='tight')
    buf.seek(0)

    # Read the image back as a PIL Image
    try:
        from PIL import Image
        img = Image.open(buf)
        img_array = np.array(img)

        # Convert to RGB if RGBA
        if img_array.shape[-1] == 4:
            img_array = img_array[:, :, :3]

        # Convert from HWC to CHW format for TensorBoard
        img_array = np.transpose(img_array, (2, 0, 1))

        buf.close()
        return img_array

    except ImportError:
        # Fallback without PIL
        buf.close()
        # Create a simple placeholder image
        placeholder = np.zeros((3, 100, 100), dtype=np.uint8)
        return placeholder


def log_embedding_metrics(
    writer: Optional[SummaryWriter],
    raw_embeddings: Any,
    projected_embeddings: Any,
    step: Optional[int] = None
):
    """
    Log embedding-related metrics to TensorBoard.

    Args:
        writer: TensorBoard SummaryWriter instance
        raw_embeddings: Raw embeddings tensor
        projected_embeddings: Projected embeddings tensor
        step: Optional step number
    """
    if writer is None or not TORCH_AVAILABLE:
        return

    try:
        # Convert to torch tensors if needed
        if not isinstance(raw_embeddings, torch.Tensor):
            raw_embeddings = torch.tensor(raw_embeddings)
        if not isinstance(projected_embeddings, torch.Tensor):
            projected_embeddings = torch.tensor(projected_embeddings)

        # Calculate and log embedding statistics
        raw_norms = torch.norm(raw_embeddings, dim=1)
        proj_norms = torch.norm(projected_embeddings, dim=1)

        # Log norm statistics
        writer.add_scalar("embeddings/raw_norm_mean", raw_norms.mean().item(), step)
        writer.add_scalar("embeddings/raw_norm_std", raw_norms.std().item(), step)
        writer.add_scalar("embeddings/projected_norm_mean", proj_norms.mean().item(), step)
        writer.add_scalar("embeddings/projected_norm_std", proj_norms.std().item(), step)

        # Log dimensionality
        writer.add_scalar("embeddings/raw_dimensions", raw_embeddings.shape[1], step)
        writer.add_scalar("embeddings/projected_dimensions", projected_embeddings.shape[1], step)

        # Log feature statistics
        raw_feature_means = torch.mean(raw_embeddings, dim=0)
        proj_feature_means = torch.mean(projected_embeddings, dim=0)

        writer.add_scalar("embeddings/raw_feature_mean_avg", raw_feature_means.mean().item(), step)
        writer.add_scalar("embeddings/raw_feature_mean_std", raw_feature_means.std().item(), step)
        writer.add_scalar("embeddings/projected_feature_mean_avg", proj_feature_means.mean().item(), step)
        writer.add_scalar("embeddings/projected_feature_mean_std", proj_feature_means.std().item(), step)

    except Exception as e:
        print(f"Warning: Failed to log embedding metrics to TensorBoard: {e}")


def finish_tensorboard(writer):
    """Finish TensorBoard logging."""
    if writer is None:
        return

    try:
        writer.close()
        print("✓ Finished TensorBoard logging")
    except Exception as e:
        print(f"Warning: Failed to finish TensorBoard logging: {e}")
