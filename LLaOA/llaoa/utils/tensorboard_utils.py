#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TensorBoard utilities for LLaOA training.
Handles TensorBoard logging setup, directory management, and metric logging.
"""

import os
import sys
import json
import psutil
import platform
import io
import numpy as np
from pathlib import Path
from typing import Optional, Dict, Any, Union
from datetime import datetime

try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_AVAILABLE = True
except ImportError:
    TENSORBOARD_AVAILABLE = False
    SummaryWriter = None

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    torch = None

try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    plt = None


class TensorBoardConfig:
    """Configuration class for TensorBoard integration."""
    
    def __init__(
        self,
        project_name: Optional[str] = None,
        run_name: Optional[str] = None,
        output_dir: str = "./checkpoints",
        logs_dir: Optional[str] = None,
        tags: Optional[list] = None,
        notes: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize TensorBoard configuration.
        
        Args:
            project_name: Project name for TensorBoard. If None, derived from run_name
            run_name: Run name for TensorBoard. If None, derived from output_dir
            output_dir: Training output directory
            logs_dir: Directory to store TensorBoard logs. If None, uses <output_dir>/logs
            tags: List of tags for the run (stored in metadata)
            notes: Notes for the run (stored in metadata)
            config: Configuration dictionary to log
        """
        self.output_dir = Path(output_dir)
        self.tags = tags or []
        self.notes = notes
        self.config = config or {}
        
        # Set project name based on run name if not provided
        if project_name is None:
            if run_name is not None:
                self.project_name = run_name
            else:
                self.project_name = self._extract_project_name_from_output_dir()
        else:
            self.project_name = project_name
            
        # Set run name based on output directory if not provided
        if run_name is None:
            self.run_name = self._extract_run_name_from_output_dir()
        else:
            self.run_name = run_name
            
        # Set logs directory
        if logs_dir is not None:
            # Use provided logs directory
            self.logs_dir = Path(logs_dir)
        else:
            # Default to output_dir/logs for backward compatibility
            self.logs_dir = self.output_dir / "logs"
            
        # Create TensorBoard specific subdirectory
        self.tensorboard_dir = self.logs_dir / "tensorboard" / self.run_name
        
        # Ensure logs directory exists
        self.tensorboard_dir.mkdir(parents=True, exist_ok=True)
        
    def _extract_project_name_from_output_dir(self) -> str:
        """Extract project name from output directory path."""
        # Get the folder name from output directory
        folder_name = self.output_dir.name
        
        # Extract meaningful project name
        if "llaoa" in folder_name.lower():
            # For names like "llaoa_projector_training_20250623_050153"
            parts = folder_name.split("_")
            if len(parts) >= 3:
                return "_".join(parts[:3])  # e.g., "llaoa_projector_training"
            else:
                return folder_name
        else:
            return "llaoa_training"
            
    def _extract_run_name_from_output_dir(self) -> str:
        """Extract run name from output directory path."""
        folder_name = self.output_dir.name
        
        # Use the full folder name as run name
        return folder_name
        
    def get_system_info(self) -> Dict[str, Any]:
        """Get system information for logging."""
        info = {
            "platform": platform.platform(),
            "python_version": sys.version,
            "cpu_count": psutil.cpu_count(),
            "memory_gb": round(psutil.virtual_memory().total / (1024**3), 2),
            "hostname": platform.node(),
        }
        
        if TORCH_AVAILABLE and torch is not None:
            info.update({
                "torch_version": torch.__version__,
                "cuda_available": torch.cuda.is_available(),
                "cuda_device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
            })
            
            if torch.cuda.is_available():
                gpu_info = []
                for i in range(torch.cuda.device_count()):
                    gpu_info.append({
                        "device_id": i,
                        "name": torch.cuda.get_device_name(i),
                        "memory_gb": round(torch.cuda.get_device_properties(i).total_memory / (1024**3), 2)
                    })
                info["gpu_info"] = gpu_info
                
        return info
    
    def save_metadata(self):
        """Save run metadata to JSON file."""
        metadata = {
            "project_name": self.project_name,
            "run_name": self.run_name,
            "tags": self.tags,
            "notes": self.notes,
            "config": self.config,
            "system_info": self.get_system_info(),
            "timestamp": datetime.now().isoformat()
        }
        
        metadata_file = self.tensorboard_dir / "metadata.json"
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2, default=str)


def initialize_tensorboard(
    tensorboard_config: TensorBoardConfig,
    model_args: Any = None,
    data_args: Any = None,
    training_args: Any = None,
    accelerator: Any = None
) -> Optional[SummaryWriter]:
    """
    Initialize TensorBoard for training logging.
    
    Args:
        tensorboard_config: TensorBoardConfig instance
        model_args: Model arguments to log
        data_args: Data arguments to log
        training_args: Training arguments to log
        accelerator: Accelerator instance for distributed training
        
    Returns:
        SummaryWriter object if successful, None otherwise
    """
    if not TENSORBOARD_AVAILABLE:
        print("Warning: TensorBoard not available, skipping TensorBoard initialization")
        return None
        
    # Only initialize TensorBoard on main process
    if accelerator is not None and not accelerator.is_main_process:
        return None
        
    try:
        # Prepare config for logging
        config_dict = tensorboard_config.config.copy()
        
        # Add system information
        config_dict["system"] = tensorboard_config.get_system_info()
        
        # Add arguments if provided
        if model_args is not None:
            config_dict["model"] = {
                k: v for k, v in vars(model_args).items() 
                if not k.startswith('_')
            }
            
        if data_args is not None:
            config_dict["data"] = {
                k: v for k, v in vars(data_args).items() 
                if not k.startswith('_')
            }
            
        if training_args is not None:
            config_dict["training"] = {
                k: v for k, v in vars(training_args).items() 
                if not k.startswith('_')
            }
        
        # Update config in tensorboard_config
        tensorboard_config.config = config_dict
        
        # Save metadata
        tensorboard_config.save_metadata()
        
        # Initialize TensorBoard SummaryWriter
        writer = SummaryWriter(
            log_dir=str(tensorboard_config.tensorboard_dir),
            comment=f"_{tensorboard_config.run_name}"
        )
        
        # Log hyperparameters as text
        hparams_text = json.dumps(config_dict, indent=2, default=str)
        writer.add_text("hyperparameters", hparams_text, 0)
        
        print(f"✓ Initialized TensorBoard project: {tensorboard_config.project_name}")
        print(f"✓ Run name: {tensorboard_config.run_name}")
        print(f"✓ Logs directory: {tensorboard_config.tensorboard_dir}")
        print(f"✓ View with: tensorboard --logdir {tensorboard_config.logs_dir}")
        
        return writer
        
    except Exception as e:
        print(f"Warning: Failed to initialize TensorBoard: {e}")
        return None


def log_metrics(writer: Optional[SummaryWriter], metrics: Dict[str, Union[float, int]], step: Optional[int] = None):
    """
    Log metrics to TensorBoard.
    
    Args:
        writer: TensorBoard SummaryWriter instance
        metrics: Dictionary of metrics to log
        step: Optional step number
    """
    if writer is None:
        return
        
    try:
        for key, value in metrics.items():
            if isinstance(value, (int, float)):
                writer.add_scalar(key, value, step)
    except Exception as e:
        print(f"Warning: Failed to log metrics to TensorBoard: {e}")


def log_model_info(writer: Optional[SummaryWriter], model: Any, tokenizer: Any = None):
    """
    Log model information to TensorBoard.
    
    Args:
        writer: TensorBoard SummaryWriter instance
        model: Model to log information about
        tokenizer: Optional tokenizer to log information about
    """
    if writer is None:
        return
        
    try:
        # Log model parameters
        if hasattr(model, 'parameters'):
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            
            writer.add_scalar("model/total_parameters", total_params, 0)
            writer.add_scalar("model/trainable_parameters", trainable_params, 0)
            writer.add_scalar("model/trainable_percentage", 
                            100 * trainable_params / total_params if total_params > 0 else 0, 0)
            
        # Log tokenizer info
        if tokenizer is not None:
            if hasattr(tokenizer, '__len__'):
                writer.add_scalar("tokenizer/vocab_size", len(tokenizer), 0)
            if hasattr(tokenizer, 'model_max_length'):
                writer.add_scalar("tokenizer/model_max_length", tokenizer.model_max_length, 0)
            
    except Exception as e:
        print(f"Warning: Failed to log model info to TensorBoard: {e}")


def log_embedding_visualization(
    writer: Optional[SummaryWriter],
    scatter_figure: Any,
    stats_figure: Any,
    step: Optional[int] = None,
    tag_prefix: str = "embeddings"
):
    """
    Log embedding visualization figures to TensorBoard.

    Args:
        writer: TensorBoard SummaryWriter instance
        scatter_figure: matplotlib Figure with scatter plot
        stats_figure: matplotlib Figure with statistics plots
        step: Optional step number
        tag_prefix: Prefix for the TensorBoard tags
    """
    if writer is None or not MATPLOTLIB_AVAILABLE:
        return

    try:
        # Convert scatter plot to image and log
        if scatter_figure is not None:
            scatter_img = figure_to_image(scatter_figure)
            writer.add_image(f"{tag_prefix}/umap_scatter", scatter_img, step)
            plt.close(scatter_figure)  # Close to free memory

        # Convert statistics plot to image and log
        if stats_figure is not None:
            stats_img = figure_to_image(stats_figure)
            writer.add_image(f"{tag_prefix}/statistics", stats_img, step)
            plt.close(stats_figure)  # Close to free memory

    except Exception as e:
        print(f"Warning: Failed to log embedding visualizations to TensorBoard: {e}")


def figure_to_image(figure: Any) -> np.ndarray:
    """
    Convert a matplotlib figure to a numpy array for TensorBoard logging.

    Args:
        figure: matplotlib Figure object

    Returns:
        numpy array with shape [C, H, W] for TensorBoard
    """
    if not MATPLOTLIB_AVAILABLE:
        raise ImportError("Matplotlib is not available")

    # Save figure to a BytesIO buffer
    buf = io.BytesIO()
    figure.savefig(buf, format='png', dpi=100, bbox_inches='tight')
    buf.seek(0)

    # Read the image back as a PIL Image
    try:
        from PIL import Image
        img = Image.open(buf)
        img_array = np.array(img)

        # Convert to RGB if RGBA
        if img_array.shape[-1] == 4:
            img_array = img_array[:, :, :3]

        # Convert from HWC to CHW format for TensorBoard
        img_array = np.transpose(img_array, (2, 0, 1))

        buf.close()
        return img_array

    except ImportError:
        # Fallback without PIL
        buf.close()
        # Create a simple placeholder image
        placeholder = np.zeros((3, 100, 100), dtype=np.uint8)
        return placeholder


def log_embedding_metrics(
    writer: Optional[SummaryWriter],
    raw_embeddings: Any,
    projected_embeddings: Any,
    step: Optional[int] = None
):
    """
    Log embedding-related metrics to TensorBoard.

    Args:
        writer: TensorBoard SummaryWriter instance
        raw_embeddings: Raw embeddings tensor
        projected_embeddings: Projected embeddings tensor
        step: Optional step number
    """
    if writer is None or not TORCH_AVAILABLE:
        return

    try:
        # Convert to torch tensors if needed
        if not isinstance(raw_embeddings, torch.Tensor):
            raw_embeddings = torch.tensor(raw_embeddings)
        if not isinstance(projected_embeddings, torch.Tensor):
            projected_embeddings = torch.tensor(projected_embeddings)

        # Calculate and log embedding statistics
        raw_norms = torch.norm(raw_embeddings, dim=1)
        proj_norms = torch.norm(projected_embeddings, dim=1)

        # Log norm statistics
        writer.add_scalar("embeddings/raw_norm_mean", raw_norms.mean().item(), step)
        writer.add_scalar("embeddings/raw_norm_std", raw_norms.std().item(), step)
        writer.add_scalar("embeddings/projected_norm_mean", proj_norms.mean().item(), step)
        writer.add_scalar("embeddings/projected_norm_std", proj_norms.std().item(), step)

        # Log dimensionality
        writer.add_scalar("embeddings/raw_dimensions", raw_embeddings.shape[1], step)
        writer.add_scalar("embeddings/projected_dimensions", projected_embeddings.shape[1], step)

        # Log feature statistics
        raw_feature_means = torch.mean(raw_embeddings, dim=0)
        proj_feature_means = torch.mean(projected_embeddings, dim=0)

        writer.add_scalar("embeddings/raw_feature_mean_avg", raw_feature_means.mean().item(), step)
        writer.add_scalar("embeddings/raw_feature_mean_std", raw_feature_means.std().item(), step)
        writer.add_scalar("embeddings/projected_feature_mean_avg", proj_feature_means.mean().item(), step)
        writer.add_scalar("embeddings/projected_feature_mean_std", proj_feature_means.std().item(), step)

    except Exception as e:
        print(f"Warning: Failed to log embedding metrics to TensorBoard: {e}")


def finish_tensorboard(writer: Optional[SummaryWriter]):
    """Finish TensorBoard logging."""
    if writer is None:
        return

    try:
        writer.close()
        print("✓ Finished TensorBoard logging")
    except Exception as e:
        print(f"Warning: Failed to finish TensorBoard logging: {e}")
