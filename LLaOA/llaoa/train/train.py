import os
import torch
import argparse
import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass
from torch.utils.data import DataLoader, DistributedSampler
from torch.nn.parallel import DistributedDataParallel as DDP
from transformers import (
    AutoTokenizer,
    get_linear_schedule_with_warmup,
    get_cosine_schedule_with_warmup,
    Trainer,
    TrainingArguments,
    HfArgumentParser
)
from accelerate import Accelerator
from accelerate.utils import set_seed
from accelerate.logging import get_logger

from llaoa.model.builder import load_pretrained_model
from llaoa.model.config import LlaoaConfig, OmicsTowerConfig
from llaoa.data.omics_qa_dataset import OmicsQADataset
from llaoa.utils.constants import DEFAULT_OMICS_TOKEN
from llaoa.utils.tensorboard_utils import TensorBoardConfig, initialize_tensorboard, log_metrics, log_model_info, finish_tensorboard, log_embedding_visualization, log_embedding_metrics
from llaoa.utils.embedding_visualization import EmbeddingVisualizationConfig, visualize_embeddings_from_dataloader

# Import TensorBoard with error handling
try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_AVAILABLE = True
except ImportError:
    TENSORBOARD_AVAILABLE = False
    SummaryWriter = None

# Set up logging
import logging
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
    handlers=[logging.StreamHandler()]
)

# Also get accelerate logger for distributed training compatibility
logger = get_logger(__name__)
# Ensure the accelerate logger also outputs to console
logger.setLevel(logging.INFO)

@dataclass
class ModelArguments:
    """
    Arguments pertaining to which model/config/tokenizer we are going to fine-tune.
    Only supports projector-only training mode.
    """
    model_path: Optional[str] = None
    model_base: Optional[str] = None
    compass_model_path: str = None  # Will be resolved at runtime
    feature_type: str = "gene_level"
    projector_type: str = "mlp2x_gelu"
    
    # Training mode - only projector-only training is supported
    tune_projector_only: bool = True  # Train only the omics projector (freeze encoder + LM)

    def __post_init__(self):
        # Resolve COMPASS model path if not explicitly provided
        if self.compass_model_path is None:
            raise ValueError("compass_model_path must be provided")

@dataclass
class DataArguments:
    """
    Arguments pertaining to what data we are going to input our model for training and eval.
    
    max_length: If None, automatically uses tokenizer.model_max_length for optimal context utilization.
    """
    rna_seq_path: str = None  # Will be resolved at runtime
    qa_json_path: str = None  # Will be resolved at runtime
    sample_id_col: Optional[str] = None
    max_length: Optional[int] = None  # If None, uses tokenizer.model_max_length
    validation_split: float = 0.1
    # Note: seed is provided by TrainingArguments, not here

    def __post_init__(self):
        # Resolve RNA-seq path if not explicitly provided
        if self.rna_seq_path is None:
            raise ValueError("rna_seq_path must be provided")

        # Resolve QA JSON path if not explicitly provided
        if self.qa_json_path is None:
            raise ValueError("qa_json_path must be provided")

@dataclass
class TrainingArguments:
    """
    Arguments for training configuration.
    """
    run_name: str = None
    output_dir: str = "./outputs"
    base_output_dir: Optional[str] = None  # Base output directory for TensorBoard logs
    report_to: str = "none"
    num_train_epochs: float = 3.0
    per_device_train_batch_size: int = 4
    per_device_eval_batch_size: int = 4
    gradient_accumulation_steps: int = 1
    learning_rate: float = 1e-5
    weight_decay: float = 0.0
    warmup_steps: int = 100
    lr_scheduler_type: str = "linear"
    logging_steps: int = 10
    eval_steps: int = 100
    save_steps: int = 500
    save_total_limit: int = 3
    resume_from_checkpoint: Optional[str] = None
    seed: int = 42
    fp16: bool = False
    bf16: bool = False
    dataloader_num_workers: int = 4
    local_rank: int = -1

    # Adam optimizer parameters
    adam_beta1: float = 0.9
    adam_beta2: float = 0.999
    adam_epsilon: float = 1e-8
    max_grad_norm: float = 1.0



def omics_collate_fn(batch, tokenizer=None):
    """
    Collate function for batching omics data and QA pairs.

    Args:
        batch: List of dictionaries with 'omics', 'input_ids', 'labels', etc.
        tokenizer: Optional tokenizer for getting pad_token_id

    Returns:
        Dictionary with batched data
    """
    # Stack omics tensors into a batch tensor
    # Each item['omics'] has shape [1, 1, num_genes]
    omics_tensors = [item['omics'] for item in batch]
    omics_batch = torch.cat(omics_tensors, dim=0)  # Shape: [batch_size, 1, num_genes]

    # Pad sequences for input_ids and labels (both have same length per sample)
    pad_token_id = getattr(tokenizer, 'pad_token_id', 0) if tokenizer is not None else 0
    input_ids = torch.nn.utils.rnn.pad_sequence(
        [item['input_ids'] for item in batch],
        batch_first=True,
        padding_value=pad_token_id
    )
    labels = torch.nn.utils.rnn.pad_sequence(
        [item['labels'] for item in batch],
        batch_first=True,
        padding_value=-100  # Standard ignore index for labels
    )

    result = {
        'omics': omics_batch,  # Tensor with shape [batch_size, 1, num_genes]
        'input_ids': input_ids,
        'labels': labels
    }
    
    # Include legacy fields only if available (for backward compatibility)
    if 'question_ids' in batch[0]:
        question_ids = torch.nn.utils.rnn.pad_sequence(
            [item['question_ids'] for item in batch],
            batch_first=True,
            padding_value=0
        )
        answer_ids = torch.nn.utils.rnn.pad_sequence(
            [item['answer_ids'] for item in batch],
            batch_first=True,
            padding_value=0
        )
        result.update({
            'question_ids': question_ids,
            'answer_ids': answer_ids
        })

    # Add attention mask for proper padding handling
    attention_mask = (input_ids != pad_token_id).long()
    result['attention_mask'] = attention_mask

    return result

def create_model_tokenizer_and_scaler(model_args, training_args):
    """
    Create or load a model, tokenizer, and scaler based on the provided arguments.

    Args:
        model_args: Model arguments
        training_args: Training arguments (for seed)

    Returns:
        Tuple of (tokenizer, model, scaler)
    """
    # Determine the model path to load from
    if model_args.model_path is not None:
        # Load existing LLaOA model
        llaoa_model_path = model_args.model_path
        print(f"Loading pretrained LLaOA model from {llaoa_model_path}")
        logger.info(f"Loading pretrained LLaOA model from {llaoa_model_path}")
    else:
        # Create new model from base language model
        if model_args.model_base is None:
            raise ValueError("Either model_path or model_base must be provided")
        
        llaoa_model_path = model_args.model_base
        print(f"Creating new LLaOA model from base language model {llaoa_model_path}")
        logger.info(f"Creating new LLaOA model from base language model {llaoa_model_path}")

    # Load model using unified function
    tokenizer, model, scaler, _ = load_pretrained_model(
        llaoa_model_path=llaoa_model_path,
        compass_model_path=model_args.compass_model_path,
        feature_type=model_args.feature_type,
        projector_type=model_args.projector_type,
        seed=training_args.seed
    )

    # Validate that we have a scaler
    if scaler is None:
        logger.warning("No scaler found - data preprocessing may not work correctly")

    # Apply projector-only training mode (only supported mode)
    print("LLaOA Projector-Only Training Mode")
    print("  - Omics encoder: FROZEN")
    print("  - Omics projector: TRAINABLE") 
    print("  - Language model: FROZEN")
    logger.info("LLaOA Projector-Only Training Mode")
    logger.info("  - Omics encoder: FROZEN")
    logger.info("  - Omics projector: TRAINABLE") 
    logger.info("  - Language model: FROZEN")
    
    # Freeze everything first
    model.requires_grad_(False)
    # Then unfreeze only the omics projector components
    for name, param in model.named_parameters():
        if any(component in name for component in ["mm_projector", "omics_projector"]):
            param.requires_grad = True
            logger.info(f"  Unfrozen: {name}")
    # Ensure omics encoder is in non-trainable mode
    if hasattr(model, 'omics_tower'):
        model.omics_tower.set_trainable(False)
    
    # Print trainable parameters summary
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"Total parameters: {total_params:,}")
    logger.info(f"Trainable parameters: {trainable_params:,}")
    logger.info(f"Trainable %: {100 * trainable_params / total_params:.2f}%")
    
    return tokenizer, model, scaler

def create_datasets(tokenizer, data_args, training_args, scaler=None):
    """
    Create training and validation datasets.

    Args:
        tokenizer: Tokenizer to use for tokenization
        data_args: Data arguments
        training_args: Training arguments (contains seed)
        scaler: Optional scaler for preprocessing RNAseq data

    Returns:
        Tuple of (train_dataset, val_dataset)
    """
    # Load full dataset
    full_dataset = OmicsQADataset(
        rna_seq_path=data_args.rna_seq_path,
        qa_json_path=data_args.qa_json_path,
        tokenizer=tokenizer,
        max_length=data_args.max_length,
        sample_id_col=data_args.sample_id_col,
        scaler=scaler  # Pass scaler to dataset for preprocessing
    )

    # Split into train and validation
    if data_args.validation_split > 0:
        # Set seed for reproducibility using training_args.seed
        np.random.seed(training_args.seed)

        # Get dataset size
        dataset_size = len(full_dataset)

        # Create indices
        indices = list(range(dataset_size))
        np.random.shuffle(indices)

        # Calculate split point
        split = int(np.floor(data_args.validation_split * dataset_size))

        # Create train and validation indices
        train_indices, val_indices = indices[split:], indices[:split]

        # Create train and validation datasets
        from torch.utils.data import Subset
        train_dataset = Subset(full_dataset, train_indices)
        val_dataset = Subset(full_dataset, val_indices)

        logger.info(f"Created train dataset with {len(train_dataset)} samples")
        logger.info(f"Created validation dataset with {len(val_dataset)} samples")

        return train_dataset, val_dataset
    else:
        logger.info(f"Using full dataset with {len(full_dataset)} samples for training")
        return full_dataset, None

def worker_init_fn(worker_id):
    """Initialize random seed for dataloader workers."""
    import random
    import numpy as np
    worker_seed = torch.initial_seed() % 2**32
    random.seed(worker_seed)
    np.random.seed(worker_seed)
    torch.manual_seed(worker_seed)

def cleanup_checkpoints(output_dir: str, save_total_limit: int, accelerator):
    """Remove old checkpoints to maintain save_total_limit."""
    if save_total_limit <= 0:
        return
    
    checkpoint_prefix = "checkpoint-"
    checkpoints = []
    
    # Find all checkpoint directories
    if os.path.exists(output_dir):
        for item in os.listdir(output_dir):
            if item.startswith(checkpoint_prefix) and os.path.isdir(os.path.join(output_dir, item)):
                try:
                    # Extract step number for sorting
                    step_part = item[len(checkpoint_prefix):]
                    if step_part.startswith("epoch-"):
                        # For epoch-based checkpoints, use a large multiplier to sort properly
                        epoch_num = int(step_part[6:])  # Remove "epoch-" prefix
                        step_num = epoch_num * 1000000  # Large multiplier to ensure epoch sorting
                    else:
                        step_num = int(step_part)
                    checkpoints.append((step_num, item))
                except ValueError:
                    continue
    
    # Sort by step number and keep only the latest save_total_limit
    checkpoints.sort(key=lambda x: x[0])
    checkpoints_to_remove = checkpoints[:-save_total_limit] if len(checkpoints) > save_total_limit else []
    
    # Remove old checkpoints
    for _, checkpoint_name in checkpoints_to_remove:
        checkpoint_path = os.path.join(output_dir, checkpoint_name)
        try:
            import shutil
            shutil.rmtree(checkpoint_path)
            accelerator.print(f"Removed old checkpoint: {checkpoint_path}")
        except Exception as e:
            accelerator.print(f"Failed to remove checkpoint {checkpoint_path}: {e}")

def train():
    """Main training function."""
    print("Starting train function...")
    
    # Parse arguments
    parser = HfArgumentParser((ModelArguments, DataArguments, TrainingArguments))
    model_args, data_args, training_args = parser.parse_args_into_dataclasses()
    
    print(f"Parsed arguments successfully. Output dir: {training_args.output_dir}")

    # Set up accelerator - temporarily disable mixed precision to avoid gradient scaler issues
    # TODO: Re-enable mixed precision once gradient scaler issues are resolved
    accelerator = Accelerator(
        gradient_accumulation_steps=training_args.gradient_accumulation_steps,
        mixed_precision="no",  # Disable mixed precision temporarily
        log_with=None  # Disable automatic logging to ensure our custom logging works
    )

    # Log mixed precision status
    if training_args.fp16 or training_args.bf16:
        accelerator.print("Warning: Mixed precision disabled due to gradient scaler compatibility issues")
        logger.warning("Mixed precision disabled due to gradient scaler compatibility issues")

    # Set seed for reproducibility
    set_seed(training_args.seed)
    
    if accelerator.is_main_process:
        print("Main process detected")
        logger.info("Setting up training on main process...")
    else:
        print(f"Process {accelerator.process_index} detected")
        logger.info(f"Setting up training on process {accelerator.process_index}...")

    # Create output directory if it doesn't exist
    if accelerator.is_main_process:
        os.makedirs(training_args.output_dir, exist_ok=True)

    # Initialize TensorBoard if enabled
    tensorboard_writer = None
    if training_args.report_to == "tensorboard" or training_args.report_to == "all":
        # Use base_output_dir if available, otherwise fall back to output_dir
        tensorboard_base_dir = training_args.base_output_dir if training_args.base_output_dir else training_args.output_dir

        # Create simplified TensorBoard configuration
        tensorboard_config = TensorBoardConfig(
            output_dir=tensorboard_base_dir,
            run_name=training_args.run_name
        )

        # Initialize TensorBoard (will be called later after model creation)
        if accelerator.is_main_process:
            logger.info("TensorBoard logging enabled")
            logger.info(f"  Logs directory: {tensorboard_config.tensorboard_dir}")
            logger.info("  Embedding visualization: enabled (automatic with TensorBoard)")

    # Create model and tokenizer
    tokenizer, model, scaler = create_model_tokenizer_and_scaler(model_args, training_args)

    # Initialize TensorBoard after model creation
    if training_args.report_to == "tensorboard" or training_args.report_to == "all":
        tensorboard_writer = initialize_tensorboard(
            tensorboard_config=tensorboard_config,
            model_args=model_args,
            data_args=data_args,
            training_args=training_args,
            accelerator=accelerator
        )

        # Log model information
        if tensorboard_writer is not None:
            log_model_info(tensorboard_writer, model, tokenizer)

    # Create datasets
    train_dataset, val_dataset = create_datasets(tokenizer, data_args, training_args, scaler)

    # Create dataloaders with tokenizer-aware collate function
    from functools import partial
    collate_fn_with_tokenizer = partial(omics_collate_fn, tokenizer=tokenizer)
    
    train_dataloader = DataLoader(
        train_dataset,
        batch_size=training_args.per_device_train_batch_size,
        shuffle=not isinstance(train_dataset, DistributedSampler),
        collate_fn=collate_fn_with_tokenizer,
        num_workers=training_args.dataloader_num_workers,
        worker_init_fn=worker_init_fn,
        pin_memory=True
    )

    if val_dataset is not None:
        eval_dataloader = DataLoader(
            val_dataset,
            batch_size=training_args.per_device_eval_batch_size,
            shuffle=False,
            collate_fn=collate_fn_with_tokenizer,
            num_workers=training_args.dataloader_num_workers,
            worker_init_fn=worker_init_fn,
            pin_memory=True
        )
    else:
        eval_dataloader = None

    # Set up optimizer (only include parameters that require gradients)
    no_decay = ["bias", "LayerNorm.weight"]
    optimizer_grouped_parameters = [
        {
            "params": [p for n, p in model.named_parameters() 
                      if p.requires_grad and not any(nd in n for nd in no_decay)],
            "weight_decay": training_args.weight_decay,
        },
        {
            "params": [p for n, p in model.named_parameters() 
                      if p.requires_grad and any(nd in n for nd in no_decay)],
            "weight_decay": 0.0,
        },
    ]
    
    # Filter out empty parameter groups
    optimizer_grouped_parameters = [group for group in optimizer_grouped_parameters if len(group["params"]) > 0]
    
    if not optimizer_grouped_parameters:
        raise ValueError("No trainable parameters found! Check your freezing configuration.")
    
    logger.info(f"Optimizer will train {sum(len(group['params']) for group in optimizer_grouped_parameters)} parameter groups")
    optimizer = torch.optim.AdamW(
        optimizer_grouped_parameters,
        lr=training_args.learning_rate,
        betas=(training_args.adam_beta1, training_args.adam_beta2),
        eps=training_args.adam_epsilon
    )

    # Set up learning rate scheduler
    num_update_steps_per_epoch = len(train_dataloader) // training_args.gradient_accumulation_steps
    max_train_steps = int(training_args.num_train_epochs * num_update_steps_per_epoch)
    num_train_epochs = int(training_args.num_train_epochs)

    if training_args.lr_scheduler_type == "cosine":
        lr_scheduler = get_cosine_schedule_with_warmup(
            optimizer=optimizer,
            num_warmup_steps=training_args.warmup_steps,
            num_training_steps=max_train_steps
        )
    else:
        lr_scheduler = get_linear_schedule_with_warmup(
            optimizer=optimizer,
            num_warmup_steps=training_args.warmup_steps,
            num_training_steps=max_train_steps
        )

    # Prepare everything with accelerator
    if eval_dataloader is not None:
        model, optimizer, train_dataloader, eval_dataloader, lr_scheduler = accelerator.prepare(
            model, optimizer, train_dataloader, eval_dataloader, lr_scheduler
        )
    else:
        model, optimizer, train_dataloader, lr_scheduler = accelerator.prepare(
            model, optimizer, train_dataloader, lr_scheduler
        )
        eval_dataloader = None

    # Handle checkpoint resuming
    starting_epoch = 0
    completed_steps = 0
    if training_args.resume_from_checkpoint is not None:
        if os.path.isdir(training_args.resume_from_checkpoint):
            accelerator.load_state(training_args.resume_from_checkpoint)
            accelerator.print(f"Resumed training from checkpoint: {training_args.resume_from_checkpoint}")
            logger.info(f"Resumed training from checkpoint: {training_args.resume_from_checkpoint}")
            
            # Extract step number from checkpoint directory name
            checkpoint_name = os.path.basename(training_args.resume_from_checkpoint.rstrip('/'))
            if checkpoint_name.startswith('checkpoint-epoch-'):
                # Epoch-based checkpoint
                epoch_num = int(checkpoint_name.split('-')[-1])
                starting_epoch = epoch_num
                completed_steps = epoch_num * (len(train_dataloader) // training_args.gradient_accumulation_steps)
            elif checkpoint_name.startswith('checkpoint-'):
                # Step-based checkpoint
                completed_steps = int(checkpoint_name.split('-')[-1])
                starting_epoch = completed_steps // (len(train_dataloader) // training_args.gradient_accumulation_steps)
            elif checkpoint_name == 'final':
                accelerator.print("Warning: Resuming from 'final' checkpoint - training may be complete")
                logger.warning("Resuming from 'final' checkpoint - training may be complete")
                
            accelerator.print(f"Resuming from epoch {starting_epoch}, step {completed_steps}")
            logger.info(f"Resuming from epoch {starting_epoch}, step {completed_steps}")
        else:
            accelerator.print(f"Warning: Checkpoint directory {training_args.resume_from_checkpoint} not found")
            logger.warning(f"Checkpoint directory {training_args.resume_from_checkpoint} not found")

    # Get total batch size for logging
    total_batch_size = (
        training_args.per_device_train_batch_size
        * accelerator.num_processes
        * training_args.gradient_accumulation_steps
    )

    # Log info - ensure it's visible
    accelerator.print("***** Running training *****")
    accelerator.print(f"  Num examples = {len(train_dataset)}")
    accelerator.print(f"  Num Epochs = {num_train_epochs}")
    accelerator.print(f"  Instantaneous batch size per device = {training_args.per_device_train_batch_size}")
    accelerator.print(f"  Total train batch size (w. parallel, distributed & accumulation) = {total_batch_size}")
    accelerator.print(f"  Gradient Accumulation steps = {training_args.gradient_accumulation_steps}")
    accelerator.print(f"  Total optimization steps = {max_train_steps}")
    
    # Also log through logger for consistency
    logger.info("***** Running training *****")
    logger.info(f"  Num examples = {len(train_dataset)}")
    logger.info(f"  Num Epochs = {num_train_epochs}")
    logger.info(f"  Instantaneous batch size per device = {training_args.per_device_train_batch_size}")
    logger.info(f"  Total train batch size (w. parallel, distributed & accumulation) = {total_batch_size}")
    logger.info(f"  Gradient Accumulation steps = {training_args.gradient_accumulation_steps}")
    logger.info(f"  Total optimization steps = {max_train_steps}")

    # Create progress bar after determining completed_steps
    try:
        # Try new accelerate API first
        from tqdm.auto import tqdm
        progress_bar = tqdm(range(completed_steps, max_train_steps), disable=not accelerator.is_local_main_process)
    except:
        # Fallback to accelerator method if it exists
        try:
            progress_bar = accelerator.get_progress_bar(max_train_steps)
            # Skip already completed steps
            for _ in range(completed_steps):
                progress_bar.update(1)
        except AttributeError:
            # Final fallback - create simple tqdm bar
            from tqdm.auto import tqdm
            progress_bar = tqdm(range(completed_steps, max_train_steps))

    # Training loop
    for epoch in range(starting_epoch, num_train_epochs):
        model.train()
        total_loss = 0

        for step, batch in enumerate(train_dataloader):
            # Forward pass
            with accelerator.accumulate(model):
                # Use pre-processed input_ids and labels from dataset
                # Input: question + answer (already concatenated)
                # Labels: -100 for question tokens, answer tokens for loss calculation
                input_ids = batch['input_ids']
                labels = batch['labels']

                outputs = model(
                    input_ids=input_ids,
                    omics_data=batch['omics'],
                    labels=labels
                )

                loss = outputs.loss

                # Check for NaN loss and skip if found
                if torch.isnan(loss):
                    accelerator.print(f"Warning: NaN loss detected at step {completed_steps}, skipping batch")
                    logger.warning(f"NaN loss detected at step {completed_steps}, skipping batch")
                    continue

                total_loss += loss.detach().float()

                # Backward pass
                accelerator.backward(loss)

                # Update weights
                if accelerator.sync_gradients:
                    # Clip gradients - Accelerate handles mixed precision properly
                    accelerator.clip_grad_norm_(model.parameters(), training_args.max_grad_norm)

                optimizer.step()
                lr_scheduler.step()
                optimizer.zero_grad()

            # All operations below should only happen once per step, not per gradient accumulation
            # Update progress bar and step counter
            if accelerator.sync_gradients:
                completed_steps += 1
                progress_bar.update(1)

                # Log loss
                if completed_steps % training_args.logging_steps == 0:
                    avg_loss = total_loss.item() / training_args.logging_steps
                    current_lr = lr_scheduler.get_last_lr()[0] if lr_scheduler else training_args.learning_rate

                    # Only print from main process to avoid duplicate logs
                    if accelerator.is_main_process:
                        accelerator.print(f"Epoch: {epoch}, Step: {completed_steps}, Loss: {avg_loss:.4f}, LR: {current_lr:.2e}")
                        logger.info(f"Epoch: {epoch}, Step: {completed_steps}, Loss: {avg_loss:.4f}, LR: {current_lr:.2e}")

                        # Log to TensorBoard
                        if training_args.report_to == "tensorboard" or training_args.report_to == "all":
                            metrics = {
                                "train/loss": avg_loss,
                                "train/learning_rate": current_lr,
                                "train/epoch": epoch,
                                "train/step": completed_steps
                            }

                            # Add system metrics if available
                            try:
                                import psutil
                                metrics.update({
                                    "system/cpu_percent": psutil.cpu_percent(),
                                    "system/memory_percent": psutil.virtual_memory().percent
                                })

                                # Add GPU metrics if available
                                if torch.cuda.is_available():
                                    for i in range(torch.cuda.device_count()):
                                        metrics[f"system/gpu_{i}_memory_percent"] = (
                                            torch.cuda.memory_allocated(i) / torch.cuda.max_memory_allocated(i) * 100
                                            if torch.cuda.max_memory_allocated(i) > 0 else 0
                                        )
                            except Exception:
                                pass  # Skip system metrics if not available

                            log_metrics(tensorboard_writer, metrics, step=completed_steps)

                    total_loss = 0

                # Save checkpoint
                if (completed_steps % int(training_args.save_steps) == 0
                    and completed_steps > 0
                    and accelerator.is_main_process):
                    output_dir = os.path.join(training_args.output_dir, f"checkpoint-{completed_steps}")
                    accelerator.save_state(output_dir)
                    accelerator.print(f"Saved checkpoint to {output_dir}")
                    logger.info(f"Saved checkpoint to {output_dir}")

                    # Clean up old checkpoints
                    cleanup_checkpoints(training_args.output_dir, training_args.save_total_limit, accelerator)

                # Evaluate
                if (eval_dataloader is not None
                    and completed_steps % int(training_args.eval_steps) == 0
                    and completed_steps > 0):

                    if accelerator.is_main_process:
                        accelerator.print(f"Starting evaluation at step {completed_steps}...")
                        logger.info(f"Starting evaluation at step {completed_steps}...")

                    model.eval()
                    eval_loss = 0
                    eval_steps = 0

                    # Limit evaluation to a subset for faster evaluation
                    max_eval_steps = min(100, len(eval_dataloader))  # Limit to 100 batches max

                    for eval_step, eval_batch in enumerate(eval_dataloader):
                        if eval_step >= max_eval_steps:
                            break

                        # Progress tracking for long evaluations
                        if eval_step % 20 == 0 and accelerator.is_main_process:
                            accelerator.print(f"  Evaluation progress: {eval_step}/{max_eval_steps}")

                        with torch.no_grad():
                            # Use streamlined dataset format (input_ids and labels already prepared)
                            eval_input_ids = eval_batch['input_ids']
                            eval_labels = eval_batch['labels']

                            outputs = model(
                                input_ids=eval_input_ids,
                                omics_data=eval_batch['omics'],
                                labels=eval_labels
                            )

                            eval_loss += outputs.loss.detach().float()
                            eval_steps += 1

                    eval_loss = eval_loss / eval_steps

                    # Only print from main process to avoid duplicate logs
                    if accelerator.is_main_process:
                        accelerator.print(f"Epoch: {epoch}, Step: {completed_steps}, Eval Loss: {eval_loss:.4f} (evaluated on {eval_steps} batches)")
                        logger.info(f"Epoch: {epoch}, Step: {completed_steps}, Eval Loss: {eval_loss:.4f} (evaluated on {eval_steps} batches)")

                        # Log evaluation metrics to TensorBoard
                        if training_args.report_to == "tensorboard" or training_args.report_to == "all":
                            eval_metrics = {
                                "eval/loss": eval_loss.item() if hasattr(eval_loss, 'item') else float(eval_loss),
                                "eval/epoch": epoch,
                                "eval/step": completed_steps,
                                "eval/batches_evaluated": eval_steps
                            }
                            log_metrics(tensorboard_writer, eval_metrics, step=completed_steps)

                        # Embedding visualization (automatic when TensorBoard is enabled)
                        if ((training_args.report_to == "tensorboard" or training_args.report_to == "all")
                            and tensorboard_writer is not None):

                            try:
                                accelerator.print(f"  Generating embedding visualizations...")
                                logger.info(f"Generating embedding visualizations at step {completed_steps}")

                                # TODO: Make these parameters configurable in the future
                                # Currently using hardcoded defaults for backward compatibility
                                embedding_config = EmbeddingVisualizationConfig(
                                    max_samples=1000,  # TODO: Make configurable
                                )
                                max_batches = 10  # TODO: Make configurable

                                # Create visualizations
                                scatter_fig, stats_fig = visualize_embeddings_from_dataloader(
                                    model=model,
                                    dataloader=eval_dataloader,
                                    device=accelerator.device,
                                    config=embedding_config,
                                    max_batches=max_batches
                                )

                                # Log to TensorBoard
                                log_embedding_visualization(
                                    tensorboard_writer,
                                    scatter_fig,
                                    stats_fig,
                                    step=completed_steps
                                )

                                accelerator.print(f"  ✓ Embedding visualizations logged to TensorBoard")
                                logger.info(f"Embedding visualizations logged at step {completed_steps}")

                            except Exception as e:
                                accelerator.print(f"  Warning: Failed to generate embedding visualizations: {e}")
                                logger.warning(f"Failed to generate embedding visualizations at step {completed_steps}: {e}")

                    model.train()

                # Break if max steps reached
                if completed_steps >= max_train_steps:
                    break

        # Save checkpoint after each epoch
        if accelerator.is_main_process:
            output_dir = os.path.join(training_args.output_dir, f"checkpoint-epoch-{epoch+1}")
            accelerator.save_state(output_dir)
            accelerator.print(f"Saved checkpoint to {output_dir}")
            logger.info(f"Saved checkpoint to {output_dir}")
            
            # Clean up old checkpoints
            cleanup_checkpoints(training_args.output_dir, training_args.save_total_limit, accelerator)

    # Save final model
    if accelerator.is_main_process:
        final_dir = os.path.join(training_args.output_dir, "final")
        accelerator.save_state(final_dir)
        accelerator.print(f"Saved final model to {final_dir}")
        logger.info(f"Saved final model to {final_dir}")

        # Unwrap model
        unwrapped_model = accelerator.unwrap_model(model)

        # Save model and tokenizer
        unwrapped_model.save_pretrained(
            final_dir,
            is_main_process=accelerator.is_main_process,
            save_function=accelerator.save
        )
        tokenizer.save_pretrained(final_dir)
        accelerator.print(f"Saved model and tokenizer to {final_dir}")
        logger.info(f"Saved model and tokenizer to {final_dir}")

    # Clean up old checkpoints
    cleanup_checkpoints(training_args.output_dir, training_args.save_total_limit, accelerator)

    # Finish TensorBoard logging
    if accelerator.is_main_process and (training_args.report_to == "tensorboard" or training_args.report_to == "all"):
        finish_tensorboard(tensorboard_writer)

if __name__ == '__main__':
    train()